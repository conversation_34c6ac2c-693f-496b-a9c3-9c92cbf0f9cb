/**
 * 后台脚本 - Service Worker
 * 处理扩展的后台逻辑和消息传递
 */

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener(function(details) {
    console.log('网页保存插件已安装');
    
    // 设置默认配置
    chrome.storage.sync.set({
        format: 'markdown',
        includeTitle: true,
        includeContent: true,
        includeLinks: true,
        includeImages: false,
        includeMeta: false
    });

    // 创建右键菜单
    createContextMenus();
});

// 创建右键菜单
function createContextMenus() {
    // 检查是否有contextMenus权限
    if (!chrome.contextMenus) {
        console.warn('contextMenus API不可用，跳过菜单创建');
        return;
    }

    try {
        // 清除现有菜单
        chrome.contextMenus.removeAll(() => {
            if (chrome.runtime.lastError) {
                console.error('清除菜单失败:', chrome.runtime.lastError);
                return;
            }

            // 主菜单
            chrome.contextMenus.create({
                id: 'webPageSaver',
                title: '网页保存插件',
                contexts: ['page']
            }, () => {
                if (chrome.runtime.lastError) {
                    console.error('创建主菜单失败:', chrome.runtime.lastError);
                    return;
                }

                // 子菜单 - 快速保存
                chrome.contextMenus.create({
                    id: 'quickSave',
                    parentId: 'webPageSaver',
                    title: '快速保存到剪贴板',
                    contexts: ['page']
                });

                // 子菜单 - 打开插件面板
                chrome.contextMenus.create({
                    id: 'openPanel',
                    parentId: 'webPageSaver',
                    title: '打开插件面板',
                    contexts: ['page']
                });

                // 子菜单 - 保存为文件
                chrome.contextMenus.create({
                    id: 'saveAsFile',
                    parentId: 'webPageSaver',
                    title: '保存为文件',
                    contexts: ['page']
                });

                // 分隔符
                chrome.contextMenus.create({
                    id: 'separator1',
                    parentId: 'webPageSaver',
                    type: 'separator',
                    contexts: ['page']
                });

                // 格式选择子菜单
                chrome.contextMenus.create({
                    id: 'formatMarkdown',
                    parentId: 'webPageSaver',
                    title: '设置为Markdown格式',
                    type: 'radio',
                    checked: true,
                    contexts: ['page']
                });

                chrome.contextMenus.create({
                    id: 'formatJSON',
                    parentId: 'webPageSaver',
                    title: '设置为JSON格式',
                    type: 'radio',
                    contexts: ['page']
                });

                chrome.contextMenus.create({
                    id: 'formatText',
                    parentId: 'webPageSaver',
                    title: '设置为纯文本格式',
                    type: 'radio',
                    contexts: ['page']
                });

                console.log('右键菜单创建完成');
            });
        });
    } catch (error) {
        console.error('创建右键菜单时出错:', error);
    }
}

// 右键菜单点击事件
if (chrome.contextMenus && chrome.contextMenus.onClicked) {
    chrome.contextMenus.onClicked.addListener(function(info, tab) {
        try {
            switch (info.menuItemId) {
                case 'quickSave':
                    handleQuickSave(tab);
                    break;
                case 'openPanel':
                    handleOpenPanel(tab);
                    break;
                case 'saveAsFile':
                    handleSaveAsFile(tab);
                    break;
                case 'formatMarkdown':
                    updateFormat('markdown');
                    break;
                case 'formatJSON':
                    updateFormat('json');
                    break;
                case 'formatText':
                    updateFormat('text');
                    break;
                default:
                    console.log('未知的菜单项:', info.menuItemId);
            }
        } catch (error) {
            console.error('处理右键菜单点击时出错:', error);
        }
    });
}

// 处理快速保存
function handleQuickSave(tab) {
    if (!tab || !tab.id) {
        console.error('无效的标签页');
        return;
    }

    chrome.tabs.sendMessage(tab.id, {action: 'extractContent'}, function(response) {
        if (chrome.runtime.lastError) {
            console.error('发送消息失败:', chrome.runtime.lastError);
            return;
        }

        if (response && response.success) {
            // 获取当前设置
            chrome.storage.sync.get({
                format: 'markdown',
                includeTitle: true,
                includeContent: true,
                includeLinks: true,
                includeImages: false,
                includeMeta: false
            }, function(settings) {
                if (chrome.runtime.lastError) {
                    console.error('获取设置失败:', chrome.runtime.lastError);
                    return;
                }

                const formattedContent = formatContent(response.content, settings);

                // 通知内容脚本复制到剪贴板
                chrome.tabs.sendMessage(tab.id, {
                    action: 'copyToClipboard',
                    content: formattedContent
                }, function(copyResponse) {
                    if (chrome.runtime.lastError) {
                        console.error('复制消息发送失败:', chrome.runtime.lastError);
                    }
                });
            });
        } else {
            console.error('内容提取失败:', response ? response.error : '未知错误');
        }
    });
}

// 处理打开面板
function handleOpenPanel(tab) {
    if (!tab || !tab.id) {
        console.error('无效的标签页');
        return;
    }

    chrome.tabs.sendMessage(tab.id, {action: 'togglePlugin'}, function(response) {
        if (chrome.runtime.lastError) {
            console.error('发送切换面板消息失败:', chrome.runtime.lastError);
        }
    });
}

// 处理保存为文件
function handleSaveAsFile(tab) {
    if (!tab || !tab.id) {
        console.error('无效的标签页');
        return;
    }

    chrome.tabs.sendMessage(tab.id, {action: 'extractContent'}, function(response) {
        if (chrome.runtime.lastError) {
            console.error('发送消息失败:', chrome.runtime.lastError);
            return;
        }

        if (response && response.success) {
            chrome.storage.sync.get({
                format: 'markdown'
            }, function(settings) {
                if (chrome.runtime.lastError) {
                    console.error('获取设置失败:', chrome.runtime.lastError);
                    return;
                }

                try {
                    const formattedContent = formatContent(response.content, settings);

                    // 创建下载
                    const blob = new Blob([formattedContent], { type: 'text/plain' });
                    const url = URL.createObjectURL(blob);

                    if (chrome.downloads) {
                        chrome.downloads.download({
                            url: url,
                            filename: `${sanitizeFilename(response.content.title)}.${getFileExtension(settings.format)}`,
                            saveAs: true
                        }, function(downloadId) {
                            if (chrome.runtime.lastError) {
                                console.error('下载失败:', chrome.runtime.lastError);
                            } else {
                                console.log('下载开始，ID:', downloadId);
                            }
                            // 清理URL
                            URL.revokeObjectURL(url);
                        });
                    } else {
                        console.error('downloads API不可用');
                    }
                } catch (error) {
                    console.error('处理下载时出错:', error);
                }
            });
        } else {
            console.error('内容提取失败:', response ? response.error : '未知错误');
        }
    });
}

// 更新格式设置
function updateFormat(format) {
    chrome.storage.sync.set({format: format}, function() {
        console.log('格式已更新为:', format);
        
        // 更新右键菜单的选中状态
        chrome.contextMenus.update('formatMarkdown', {checked: format === 'markdown'});
        chrome.contextMenus.update('formatJSON', {checked: format === 'json'});
        chrome.contextMenus.update('formatText', {checked: format === 'text'});
    });
}

// 格式化内容（简化版本，主要逻辑在popup.js中）
function formatContent(content, settings) {
    // 这里可以实现简化的格式化逻辑
    // 或者直接返回JSON格式，由前端处理
    return JSON.stringify(content, null, 2);
}

// 工具函数
function sanitizeFilename(filename) {
    return filename.replace(/[^\w\s-]/g, '').trim().replace(/\s+/g, '_');
}

function getFileExtension(format) {
    const extensions = {
        'markdown': 'md',
        'json': 'json',
        'text': 'txt'
    };
    return extensions[format] || 'txt';
}

// 监听来自内容脚本的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === 'getSettings') {
        chrome.storage.sync.get({
            format: 'markdown',
            includeTitle: true,
            includeContent: true,
            includeLinks: true,
            includeImages: false,
            includeMeta: false
        }, function(settings) {
            sendResponse(settings);
        });
        return true;
    }
    
    if (request.action === 'saveSettings') {
        chrome.storage.sync.set(request.settings, function() {
            sendResponse({success: true});
        });
        return true;
    }
});

// 扩展启动时初始化
chrome.runtime.onStartup.addListener(function() {
    console.log('网页保存插件已启动');
});
