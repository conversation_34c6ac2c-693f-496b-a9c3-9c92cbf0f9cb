/**
 * 网页内容保存插件 - 主入口文件
 * 功能：将当前网页信息保存为美化的AI输出格式
 */

class WebPageSaver {
    constructor() {
        this.init();
    }

    init() {
        this.createUI();
        this.bindEvents();
        console.log('网页保存插件已初始化');
    }

    // 创建用户界面
    createUI() {
        // 创建浮动按钮
        const floatBtn = document.createElement('div');
        floatBtn.id = 'web-saver-float-btn';
        floatBtn.innerHTML = '💾';
        floatBtn.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: #007bff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10000;
            font-size: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        `;

        // 创建主面板
        const panel = document.createElement('div');
        panel.id = 'web-saver-panel';
        panel.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            width: 350px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            z-index: 10001;
            display: none;
            font-family: Arial, sans-serif;
        `;

        panel.innerHTML = `
            <div style="padding: 15px; border-bottom: 1px solid #eee;">
                <h3 style="margin: 0; color: #333;">网页内容保存</h3>
            </div>
            <div style="padding: 15px;">
                <div style="margin-bottom: 10px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">保存格式:</label>
                    <select id="save-format" style="width: 100%; padding: 5px; border: 1px solid #ddd; border-radius: 4px;">
                        <option value="markdown">Markdown格式</option>
                        <option value="json">JSON格式</option>
                        <option value="text">纯文本格式</option>
                    </select>
                </div>
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">包含内容:</label>
                    <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                        <label><input type="checkbox" id="include-title" checked> 标题</label>
                        <label><input type="checkbox" id="include-content" checked> 正文</label>
                        <label><input type="checkbox" id="include-links" checked> 链接</label>
                        <label><input type="checkbox" id="include-images"> 图片</label>
                        <label><input type="checkbox" id="include-meta"> 元信息</label>
                    </div>
                </div>
                <div style="display: flex; gap: 10px;">
                    <button id="save-to-clipboard" style="flex: 1; padding: 8px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">复制到剪贴板</button>
                    <button id="save-to-file" style="flex: 1; padding: 8px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">下载文件</button>
                </div>
                <div style="margin-top: 10px;">
                    <button id="preview-content" style="width: 100%; padding: 8px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">预览内容</button>
                </div>
            </div>
        `;

        document.body.appendChild(floatBtn);
        document.body.appendChild(panel);

        this.floatBtn = floatBtn;
        this.panel = panel;
    }

    // 绑定事件
    bindEvents() {
        // 浮动按钮点击事件
        this.floatBtn.addEventListener('click', () => {
            const isVisible = this.panel.style.display !== 'none';
            this.panel.style.display = isVisible ? 'none' : 'block';
        });

        // 复制到剪贴板
        document.getElementById('save-to-clipboard').addEventListener('click', () => {
            this.saveToClipboard();
        });

        // 下载文件
        document.getElementById('save-to-file').addEventListener('click', () => {
            this.saveToFile();
        });

        // 预览内容
        document.getElementById('preview-content').addEventListener('click', () => {
            this.previewContent();
        });

        // 点击外部关闭面板
        document.addEventListener('click', (e) => {
            if (!this.panel.contains(e.target) && !this.floatBtn.contains(e.target)) {
                this.panel.style.display = 'none';
            }
        });
    }

    // 提取网页内容
    extractPageContent() {
        const content = {
            url: window.location.href,
            title: document.title,
            timestamp: new Date().toISOString(),
            domain: window.location.hostname,
            content: '',
            links: [],
            images: [],
            meta: {}
        };

        // 提取正文内容
        const includeContent = document.getElementById('include-content').checked;
        if (includeContent) {
            content.content = this.extractMainContent();
        }

        // 提取链接
        const includeLinks = document.getElementById('include-links').checked;
        if (includeLinks) {
            content.links = this.extractLinks();
        }

        // 提取图片
        const includeImages = document.getElementById('include-images').checked;
        if (includeImages) {
            content.images = this.extractImages();
        }

        // 提取元信息
        const includeMeta = document.getElementById('include-meta').checked;
        if (includeMeta) {
            content.meta = this.extractMetaInfo();
        }

        return content;
    }

    // 提取主要内容 - 使用智能算法，不依赖CSS类
    extractMainContent() {
        // 1. 首先尝试语义化HTML标签（优先级最高）
        const semanticSelectors = [
            'article',
            'main',
            '[role="main"]',
            '[role="article"]'
        ];

        for (const selector of semanticSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                const content = this.extractTextWithStructure(element);
                if (content.length > 200) { // 确保内容足够丰富
                    return content.substring(0, 10000); // 增加长度限制
                }
            }
        }

        // 2. 如果没有找到语义化标签，使用内容密度分析
        const contentCandidate = this.findContentByDensity();
        if (contentCandidate && contentCandidate.length > 200) {
            return contentCandidate.substring(0, 10000);
        }

        // 3. 最后降级到body，但过滤掉导航、侧边栏等
        const bodyContent = this.extractTextWithStructure(document.body, true);
        return bodyContent.substring(0, 10000);
    }

    // 清理文本
    cleanText(text) {
        return text
            .replace(/\s+/g, ' ')
            .replace(/\n\s*\n/g, '\n')
            .trim();
    }

    // 提取文本并保持基本结构
    extractTextWithStructure(element, filterNoise = false) {
        if (!element) return '';

        // 克隆元素以避免修改原DOM
        const clone = element.cloneNode(true);

        // 移除不需要的元素
        const unwantedSelectors = [
            'script', 'style', 'noscript', 'iframe',
            'header', 'footer', 'aside', 'nav',
            '.advertisement', '.ads', '.sidebar',
            '[role="banner"]', '[role="navigation"]', '[role="complementary"]'
        ];

        if (filterNoise) {
            // 在body级别提取时，移除更多噪音元素
            unwantedSelectors.push(
                '.menu', '.navbar', '.header', '.footer',
                '.social', '.share', '.comment', '.related'
            );
        }

        unwantedSelectors.forEach(selector => {
            const elements = clone.querySelectorAll(selector);
            elements.forEach(el => el.remove());
        });

        // 处理各种HTML元素，保持结构
        const processElement = (el) => {
            let text = '';

            for (const child of el.childNodes) {
                if (child.nodeType === Node.TEXT_NODE) {
                    const textContent = child.textContent.trim();
                    if (textContent) {
                        text += textContent + ' ';
                    }
                } else if (child.nodeType === Node.ELEMENT_NODE) {
                    const tagName = child.tagName.toLowerCase();

                    // 处理标题
                    if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) {
                        const headingText = child.textContent.trim();
                        if (headingText) {
                            text += '\n\n## ' + headingText + '\n\n';
                        }
                    }
                    // 处理段落
                    else if (tagName === 'p') {
                        const pText = child.textContent.trim();
                        if (pText) {
                            text += pText + '\n\n';
                        }
                    }
                    // 处理列表
                    else if (tagName === 'li') {
                        const liText = child.textContent.trim();
                        if (liText) {
                            text += '• ' + liText + '\n';
                        }
                    }
                    // 处理换行
                    else if (tagName === 'br') {
                        text += '\n';
                    }
                    // 处理块级元素
                    else if (['div', 'section', 'blockquote', 'pre'].includes(tagName)) {
                        const blockText = processElement(child);
                        if (blockText.trim()) {
                            text += blockText + '\n';
                        }
                    }
                    // 处理其他元素
                    else {
                        const childText = processElement(child);
                        if (childText.trim()) {
                            text += childText + ' ';
                        }
                    }
                }
            }

            return text;
        };

        const result = processElement(clone);
        return this.cleanText(result);
    }

    // 基于内容密度查找主要内容区域
    findContentByDensity() {
        const allElements = document.querySelectorAll('div, section, article');
        let bestElement = null;
        let bestScore = 0;

        for (const element of allElements) {
            // 跳过明显的非内容元素
            const classList = element.className.toLowerCase();
            const id = element.id.toLowerCase();

            if (this.isNoiseElement(classList, id)) {
                continue;
            }

            // 计算内容密度分数
            const score = this.calculateContentScore(element);

            if (score > bestScore) {
                bestScore = score;
                bestElement = element;
            }
        }

        return bestElement ? this.extractTextWithStructure(bestElement) : '';
    }

    // 判断是否为噪音元素
    isNoiseElement(classList, id) {
        const noiseKeywords = [
            'nav', 'menu', 'sidebar', 'footer', 'header',
            'advertisement', 'ads', 'social', 'share',
            'comment', 'related', 'popup', 'modal'
        ];

        const text = (classList + ' ' + id).toLowerCase();
        return noiseKeywords.some(keyword => text.includes(keyword));
    }

    // 计算内容质量分数
    calculateContentScore(element) {
        const text = element.textContent || '';
        const textLength = text.length;

        if (textLength < 50) return 0;

        // 基础分数：文本长度
        let score = textLength;

        // 段落数量加分
        const paragraphs = element.querySelectorAll('p');
        score += paragraphs.length * 50;

        // 标题加分
        const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
        score += headings.length * 30;

        // 链接密度惩罚（链接过多可能是导航区域）
        const links = element.querySelectorAll('a');
        const linkTextLength = Array.from(links).reduce((sum, link) => sum + (link.textContent || '').length, 0);
        const linkDensity = linkTextLength / textLength;
        if (linkDensity > 0.3) {
            score *= (1 - linkDensity);
        }

        // 图片加分
        const images = element.querySelectorAll('img');
        score += images.length * 20;

        // 嵌套深度惩罚（过深的嵌套可能是布局元素）
        const depth = this.getElementDepth(element);
        if (depth > 10) {
            score *= 0.8;
        }

        return score;
    }

    // 获取元素嵌套深度
    getElementDepth(element) {
        let depth = 0;
        let current = element;
        while (current.parentElement) {
            depth++;
            current = current.parentElement;
        }
        return depth;
    }

    // 提取链接
    extractLinks() {
        const links = [];
        const linkElements = document.querySelectorAll('a[href]');

        linkElements.forEach(link => {
            const href = link.href;
            const text = link.textContent.trim();

            if (href && text && !href.startsWith('javascript:') && !href.startsWith('#')) {
                links.push({
                    url: href,
                    text: text,
                    title: link.title || ''
                });
            }
        });

        // 去重并限制数量
        const uniqueLinks = links.filter((link, index, self) =>
            index === self.findIndex(l => l.url === link.url)
        ).slice(0, 50);

        return uniqueLinks;
    }

    // 提取图片
    extractImages() {
        const images = [];
        const imgElements = document.querySelectorAll('img[src]');

        imgElements.forEach(img => {
            if (img.src && !img.src.startsWith('data:')) {
                images.push({
                    url: img.src,
                    alt: img.alt || '',
                    title: img.title || ''
                });
            }
        });

        return images.slice(0, 20); // 限制数量
    }

    // 提取元信息
    extractMetaInfo() {
        const meta = {};

        // 基本信息
        meta.charset = document.characterSet;
        meta.language = document.documentElement.lang;

        // Meta标签
        const metaTags = document.querySelectorAll('meta');
        metaTags.forEach(tag => {
            const name = tag.getAttribute('name') || tag.getAttribute('property');
            const content = tag.getAttribute('content');

            if (name && content) {
                meta[name] = content;
            }
        });

        return meta;
    }

    // 格式化内容
    formatContent(content, format) {
        switch (format) {
            case 'markdown':
                return this.formatAsMarkdown(content);
            case 'json':
                return this.formatAsJSON(content);
            case 'text':
                return this.formatAsText(content);
            default:
                return this.formatAsMarkdown(content);
        }
    }

    // Markdown格式
    formatAsMarkdown(content) {
        let markdown = '';

        // 标题
        if (document.getElementById('include-title').checked) {
            markdown += `# ${content.title}\n\n`;
        }

        // 基本信息
        markdown += `**网址**: ${content.url}\n`;
        markdown += `**域名**: ${content.domain}\n`;
        markdown += `**保存时间**: ${new Date(content.timestamp).toLocaleString('zh-CN')}\n\n`;

        // 正文内容
        if (content.content) {
            markdown += `## 正文内容\n\n${content.content}\n\n`;
        }

        // 链接
        if (content.links && content.links.length > 0) {
            markdown += `## 相关链接\n\n`;
            content.links.forEach((link, index) => {
                markdown += `${index + 1}. [${link.text}](${link.url})\n`;
            });
            markdown += '\n';
        }

        // 图片
        if (content.images && content.images.length > 0) {
            markdown += `## 图片资源\n\n`;
            content.images.forEach((img, index) => {
                markdown += `${index + 1}. ![${img.alt}](${img.url})\n`;
            });
            markdown += '\n';
        }

        // 元信息
        if (content.meta && Object.keys(content.meta).length > 0) {
            markdown += `## 元信息\n\n`;
            Object.entries(content.meta).forEach(([key, value]) => {
                markdown += `- **${key}**: ${value}\n`;
            });
        }

        return markdown;
    }

    // JSON格式
    formatAsJSON(content) {
        return JSON.stringify(content, null, 2);
    }

    // 纯文本格式
    formatAsText(content) {
        let text = '';

        // 标题
        if (document.getElementById('include-title').checked) {
            text += `标题: ${content.title}\n`;
        }

        // 基本信息
        text += `网址: ${content.url}\n`;
        text += `域名: ${content.domain}\n`;
        text += `保存时间: ${new Date(content.timestamp).toLocaleString('zh-CN')}\n\n`;

        // 正文内容
        if (content.content) {
            text += `正文内容:\n${content.content}\n\n`;
        }

        // 链接
        if (content.links && content.links.length > 0) {
            text += `相关链接:\n`;
            content.links.forEach((link, index) => {
                text += `${index + 1}. ${link.text}: ${link.url}\n`;
            });
            text += '\n';
        }

        // 图片
        if (content.images && content.images.length > 0) {
            text += `图片资源:\n`;
            content.images.forEach((img, index) => {
                text += `${index + 1}. ${img.alt}: ${img.url}\n`;
            });
            text += '\n';
        }

        return text;
    }

    // 复制到剪贴板
    async saveToClipboard() {
        try {
            const content = this.extractPageContent();
            const format = document.getElementById('save-format').value;
            const formattedContent = this.formatContent(content, format);

            await navigator.clipboard.writeText(formattedContent);
            this.showMessage('内容已复制到剪贴板！', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            this.showMessage('复制失败，请重试', 'error');
        }
    }

    // 保存到文件
    saveToFile() {
        try {
            const content = this.extractPageContent();
            const format = document.getElementById('save-format').value;
            const formattedContent = this.formatContent(content, format);

            const blob = new Blob([formattedContent], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `${this.sanitizeFilename(content.title)}.${this.getFileExtension(format)}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.showMessage('文件下载已开始！', 'success');
        } catch (error) {
            console.error('保存失败:', error);
            this.showMessage('保存失败，请重试', 'error');
        }
    }

    // 预览内容
    previewContent() {
        const content = this.extractPageContent();
        const format = document.getElementById('save-format').value;
        const formattedContent = this.formatContent(content, format);

        // 创建预览窗口
        const previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
        previewWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>内容预览</title>
                <meta charset="utf-8">
                <style>
                    body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
                    pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; }
                    .header { border-bottom: 2px solid #007bff; padding-bottom: 10px; margin-bottom: 20px; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>网页内容预览</h1>
                    <p>格式: ${format.toUpperCase()}</p>
                </div>
                <pre>${this.escapeHtml(formattedContent)}</pre>
            </body>
            </html>
        `);
        previewWindow.document.close();
    }

    // 工具方法
    sanitizeFilename(filename) {
        return filename.replace(/[^\w\s-]/g, '').trim().replace(/\s+/g, '_');
    }

    getFileExtension(format) {
        const extensions = {
            'markdown': 'md',
            'json': 'json',
            'text': 'txt'
        };
        return extensions[format] || 'txt';
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 显示消息
    showMessage(message, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 15px 25px;
            background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
            color: white;
            border-radius: 5px;
            z-index: 10002;
            font-family: Arial, sans-serif;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        `;
        messageDiv.textContent = message;

        document.body.appendChild(messageDiv);

        setTimeout(() => {
            document.body.removeChild(messageDiv);
        }, 3000);
    }
}

// 初始化插件
if (typeof window !== 'undefined') {
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            window.webPageSaverInstance = new WebPageSaver();
        });
    } else {
        window.webPageSaverInstance = new WebPageSaver();
    }
}