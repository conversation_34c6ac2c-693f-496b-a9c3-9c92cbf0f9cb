!(async function () {
  if (location.href.indexOf("/live/page/") == -1) return;
  function injectScriptBySrc(src) {
    const script = document.createElement("script");
    script.setAttribute("src", src);
    var html = document.getElementsByTagName("html")[0];
    html.appendChild(script);
    html.removeChild(script);
  }

  function injectCode(path) {
    const src = chrome.runtime.getURL(path);
    injectScriptBySrc(src);
  }
  const jsList = [
    "./utils/vue.min.js",
    "./utils/element-ui.min.css",
    "./utils/element-ui.min.js",
  ];

  function request(url) {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open("GET", url);
      xhr.onload = function () {
        if (this.status === 200) {
          resolve(this.response);
        } else {
          reject(new Error(this.statusText));
        }
      };
      xhr.onerror = function () {
        reject(new Error(this.statusText));
      };
      xhr.send();
    });
  }

  async function Init() {
    const html = await request(chrome.runtime.getURL("./utils/user.html"));
    const requestDiv = document.createElement("div");
    requestDiv.innerHTML = html;
    const template = requestDiv.children[0];
    const innerDiv = document.createElement("div");
    innerDiv.id = "wind_app_container";
    innerDiv.innerHTML = template.innerHTML;
    document.querySelector("html").appendChild(innerDiv);
  }

  await Init();
  injectCode("./utils/init.js");

  for (let index = 0; index < jsList.length; index++) {
    const item = jsList[index];
    if (item.indexOf(".css") > -1) {
      const style = document.createElement("link");
      style.setAttribute("rel", "stylesheet");
      style.setAttribute("href", chrome.runtime.getURL(item));
      var html = document.getElementsByTagName("html")[0];
      html.appendChild(style);
      // html.removeChild(style);
      continue;
    }
    injectCode(item);
    await new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 1000);
    });
  }
})();
