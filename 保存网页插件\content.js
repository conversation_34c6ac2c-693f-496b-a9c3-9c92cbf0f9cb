/**
 * 内容脚本 - 注入到网页中的脚本
 * 负责在网页中加载主插件功能
 */

// 避免重复注入
if (!window.webPageSaverInjected) {
    window.webPageSaverInjected = true;

    // 动态加载主插件脚本
    function loadWebPageSaver() {
        // 检查是否已经存在插件实例
        if (document.getElementById('web-saver-float-btn')) {
            return;
        }

        // 创建script标签加载主插件
        const script = document.createElement('script');
        script.src = chrome.runtime.getURL('index.js');
        script.onload = function() {
            console.log('网页保存插件已加载');
            this.remove();
        };
        script.onerror = function() {
            console.error('网页保存插件加载失败');
            this.remove();
        };
        
        (document.head || document.documentElement).appendChild(script);
    }

    // 等待DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadWebPageSaver);
    } else {
        loadWebPageSaver();
    }

    // 监听来自popup的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        if (request.action === 'togglePlugin') {
            const floatBtn = document.getElementById('web-saver-float-btn');
            if (floatBtn) {
                floatBtn.click();
            } else {
                loadWebPageSaver();
            }
            sendResponse({success: true});
        }
        
        if (request.action === 'extractContent') {
            try {
                // 如果插件已加载，直接提取内容
                if (window.webPageSaverInstance) {
                    const content = window.webPageSaverInstance.extractPageContent();
                    sendResponse({success: true, content: content});
                } else {
                    sendResponse({success: false, error: '插件未加载'});
                }
            } catch (error) {
                sendResponse({success: false, error: error.message});
            }
        }
        
        return true; // 保持消息通道开放
    });
}
