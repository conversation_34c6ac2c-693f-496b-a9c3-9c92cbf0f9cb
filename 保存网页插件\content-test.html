<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能网页内容提取测试 - 中文标题示例页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        .header {
            background: #333;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .nav {
            background: #666;
            padding: 10px;
        }
        .nav a {
            color: white;
            text-decoration: none;
            margin: 0 15px;
        }
        .container {
            display: flex;
            max-width: 1200px;
            margin: 20px auto;
            gap: 20px;
        }
        .sidebar {
            width: 250px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            height: fit-content;
        }
        .main-content {
            flex: 1;
            background: white;
            padding: 30px;
            border-radius: 8px;
            line-height: 1.6;
        }
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 40px;
        }
        .advertisement {
            background: #ffeb3b;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
            border: 2px dashed #ff9800;
        }
        .social-share {
            background: #e3f2fd;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
        .related-articles {
            background: #f3e5f5;
            padding: 20px;
            margin: 20px 0;
        }
        h1, h2, h3 {
            color: #333;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header">
        <h1>测试网站标题</h1>
        <p>这是一个用于测试内容提取的复杂布局网页</p>
    </header>

    <!-- 导航栏 -->
    <nav class="nav">
        <a href="#home">首页</a>
        <a href="#about">关于我们</a>
        <a href="#services">服务</a>
        <a href="#contact">联系我们</a>
        <a href="#blog">博客</a>
    </nav>

    <div class="container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <h3>侧边栏导航</h3>
            <ul>
                <li><a href="#category1">分类一</a></li>
                <li><a href="#category2">分类二</a></li>
                <li><a href="#category3">分类三</a></li>
            </ul>

            <div class="advertisement">
                <h4>广告区域</h4>
                <p>这里是广告内容，应该被过滤掉</p>
            </div>

            <h3>热门文章</h3>
            <ul>
                <li><a href="#hot1">热门文章1</a></li>
                <li><a href="#hot2">热门文章2</a></li>
                <li><a href="#hot3">热门文章3</a></li>
            </ul>
        </aside>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <article>
                <h1>这是主要文章标题</h1>
                <p><strong>发布时间：</strong>2025年7月2日 | <strong>作者：</strong>测试作者</p>

                <h2>文章简介</h2>
                <p>这是一篇用于测试网页保存插件内容提取功能的示例文章。文章包含了多种HTML元素，用于验证插件是否能够正确提取主要内容，同时过滤掉导航、广告、侧边栏等噪音信息。</p>

                <h2>主要内容部分</h2>
                <p>在现代网页开发中，内容提取是一个重要的技术挑战。不同的网站使用不同的CSS类名和HTML结构，这使得基于固定选择器的提取方法变得不可靠。</p>

                <h3>智能内容提取的优势</h3>
                <p>智能内容提取算法具有以下优势：</p>
                <ul>
                    <li>不依赖特定的CSS类名</li>
                    <li>基于内容密度分析</li>
                    <li>保持原有的文本结构</li>
                    <li>自动过滤噪音元素</li>
                </ul>

                <h3>技术实现原理</h3>
                <p>该算法主要包含以下几个步骤：</p>
                <ol>
                    <li><strong>语义化标签优先：</strong>首先查找article、main等语义化HTML标签</li>
                    <li><strong>内容密度分析：</strong>计算每个元素的文本密度和质量分数</li>
                    <li><strong>噪音过滤：</strong>识别并移除导航、广告、侧边栏等元素</li>
                    <li><strong>结构保持：</strong>在提取过程中保持标题、段落、列表等结构</li>
                </ol>

                <blockquote>
                    <p>"好的内容提取算法应该像人类阅读一样，能够识别页面的主要内容，忽略无关的装饰性元素。"</p>
                </blockquote>

                <h2>测试功能</h2>
                <p>点击下面的按钮来测试不同的内容提取方法：</p>
                
                <button class="test-button" onclick="testOriginalMethod()">测试原始方法</button>
                <button class="test-button" onclick="testNewMethod()">测试新方法</button>
                <button class="test-button" onclick="compareResults()">对比结果</button>
                <button class="test-button" onclick="testFilename()">测试文件名生成</button>

                <div id="test-results" class="result" style="display: none;"></div>

                <h2>结论</h2>
                <p>通过使用智能内容提取算法，我们可以更准确地获取网页的主要内容，为AI处理和用户阅读提供更好的体验。这种方法特别适用于需要处理多种不同网站结构的场景。</p>
            </article>

            <!-- 社交分享区域 -->
            <div class="social-share">
                <h4>分享这篇文章</h4>
                <button>分享到微信</button>
                <button>分享到微博</button>
                <button>分享到QQ</button>
            </div>

            <!-- 相关文章推荐 -->
            <div class="related-articles">
                <h3>相关文章推荐</h3>
                <ul>
                    <li><a href="#related1">相关文章1：网页内容分析技术</a></li>
                    <li><a href="#related2">相关文章2：HTML语义化最佳实践</a></li>
                    <li><a href="#related3">相关文章3：前端数据提取方法</a></li>
                </ul>
            </div>
        </main>
    </div>

    <!-- 页面底部 -->
    <footer class="footer">
        <p>&copy; 2025 测试网站. 保留所有权利.</p>
        <p>联系我们: <EMAIL> | 电话: ************</p>
    </footer>

    <!-- 加载插件脚本 -->
    <script src="./index.js"></script>

    <script>
        // 测试原始方法（模拟）
        function testOriginalMethod() {
            const result = document.getElementById('test-results');
            result.style.display = 'block';
            
            // 模拟原始的基于CSS类的提取方法
            const selectors = ['.content', '.main-content', '.post-content', 'article', 'main'];
            let content = '';
            
            for (const selector of selectors) {
                const element = document.querySelector(selector);
                if (element) {
                    content = element.innerText; // 移除长度限制
                    break;
                }
            }
            
            result.textContent = '原始方法提取结果:\n' + content;
        }

        // 测试新方法
        function testNewMethod() {
            const result = document.getElementById('test-results');
            result.style.display = 'block';
            
            if (window.webPageSaverInstance) {
                const content = window.webPageSaverInstance.extractMainContent();
                result.textContent = '新方法提取结果:\n' + content;
            } else {
                result.textContent = '插件未加载，请先加载插件';
            }
        }

        // 对比两种方法的结果
        function compareResults() {
            const result = document.getElementById('test-results');
            result.style.display = 'block';
            
            // 原始方法
            const selectors = ['.content', '.main-content', '.post-content', 'article', 'main'];
            let originalContent = '';
            for (const selector of selectors) {
                const element = document.querySelector(selector);
                if (element) {
                    originalContent = element.innerText; // 移除长度限制
                    break;
                }
            }
            
            // 新方法
            let newContent = '';
            if (window.webPageSaverInstance) {
                newContent = window.webPageSaverInstance.extractMainContent(); // 移除长度限制
            }
            
            result.textContent = `对比结果:
            
=== 原始方法 ===
${originalContent}

=== 新方法 ===
${newContent}

=== 分析 ===
原始方法长度: ${originalContent.length}
新方法长度: ${newContent.length}
新方法是否包含更多结构化信息: ${newContent.includes('##') ? '是' : '否'}`;
        }

        // 测试文件名生成功能
        function testFilename() {
            const result = document.getElementById('test-results');
            result.style.display = 'block';

            // 模拟sanitizeFilename函数
            function sanitizeFilename(filename) {
                if (!filename || typeof filename !== 'string') {
                    return '未命名页面';
                }

                return filename
                    .replace(/[<>:"/\\|?*]/g, '') // 移除Windows文件名禁用字符
                    .replace(/[\x00-\x1f\x80-\x9f]/g, '') // 移除控制字符
                    .trim()
                    .replace(/\s+/g, '_') // 将空格替换为下划线
                    .substring(0, 100); // 限制文件名长度
            }

            const pageTitle = document.title;
            const sanitizedTitle = sanitizeFilename(pageTitle);
            const domain = window.location.hostname;

            result.textContent = `文件名生成测试:

原始标题: ${pageTitle}
处理后文件名: ${sanitizedTitle}
域名: ${domain}

最终文件名示例:
- Markdown格式: ${sanitizedTitle}.md
- JSON格式: ${sanitizedTitle}.json
- 文本格式: ${sanitizedTitle}.txt

文件名特点:
- 保留中文字符: ✅
- 移除特殊字符: ✅
- 空格转下划线: ✅
- 长度限制: ${sanitizedTitle.length}/100 字符`;
        }
    </script>
</body>
</html>
